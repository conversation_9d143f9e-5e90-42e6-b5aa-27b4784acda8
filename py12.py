# Demonstrates how `__new__` runs before `__init__` during instantiation.
class Persion:
  def __init__(self):
    # Called after `__new__` returns the newly created instance.
    print("Persion init")
    pass
  
  def __new__(cls):
    # Create the new instance and allow custom allocation work before `__init__`.
    print("Persion new")
    return object.__new__(cls)

# Instantiating triggers `__new__` first, then `__init__`.
p = Persion()
