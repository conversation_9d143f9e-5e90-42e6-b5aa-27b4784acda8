class Person:

  name = "人类"

  def eat(self):
    print("人类在吃饭")

  def sleep(self):
    print("人类在睡觉")

  def work(self):
    print("人类在工作")

class Mother(Person):
  def cook(self):
    print("我会做饭")

class Father(Person):
  def money(self):
    print("我会赚钱")

class Girl(Mother, Father):

  name = "女孩"

  def eat(self):
    print("女孩在吃饭")

class Boy(Person):
  pass

g = Girl()
print(g.name)
g.eat()

g.money()

b = Boy()
b.work()
b.sleep()

